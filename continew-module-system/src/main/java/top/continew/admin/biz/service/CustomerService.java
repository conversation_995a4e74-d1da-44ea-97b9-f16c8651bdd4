/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service;

import jakarta.servlet.http.HttpServletResponse;
import top.continew.admin.biz.enums.CustomerBalanceTypeEnum;
import top.continew.admin.biz.enums.RechargeFeeHandleMethodEnum;
import top.continew.admin.biz.excel.CustomerDailyExcel;
import top.continew.admin.biz.model.entity.CustomerActivationReq;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.query.CustomerOrderStatisticsQuery;
import top.continew.admin.biz.model.query.CustomerQuery;
import top.continew.admin.biz.model.query.CustomerStatReportQuery;
import top.continew.admin.biz.model.req.CustomerBalanceChangeReq;
import top.continew.admin.biz.model.req.CustomerOpenReq;
import top.continew.admin.biz.model.req.CustomerReq;
import top.continew.admin.biz.model.req.ToufangRefundReq;
import top.continew.admin.biz.model.resp.*;
import top.continew.starter.data.mp.service.IService;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户业务接口
 *
 * <AUTHOR>
 * @since 2024/12/30 17:56
 */
public interface CustomerService extends BaseService<CustomerResp, CustomerDetailResp, CustomerQuery, CustomerReq>, IService<CustomerDO> {

    /**
     * 客户余额变更
     *
     * @param customerId
     * @param amount          金额为负数时代表支出
     * @param balanceType
     * @param transactionTime
     * @param remark
     * @return
     */
    Long changeAmount(Long customerId,
                      BigDecimal amount,
                      CustomerBalanceTypeEnum balanceType,
                      LocalDateTime transactionTime,
                      String remark);

    /**
     * 客户余额变更
     *
     * @param customerId
     * @param platformAdId
     * @param amount          金额为负数时代表支出
     * @param balanceType
     * @param transactionTime
     * @param remark
     * @return
     */
    Long changeAmount(Long customerId,
                      String platformAdId,
                      BigDecimal amount,
                      CustomerBalanceTypeEnum balanceType,
                      LocalDateTime transactionTime,
                      String remark);

    /**
     * 后台修改余额
     *
     * @param id
     * @param req
     */
    void changeAmount(Long id, CustomerBalanceChangeReq req);

    /**
     * 获取客户统计数据
     *
     * @param query
     * @param pageQuery
     * @return
     */
    PageResp<CustomerStatReportResp> pageCustomerStatReport(CustomerStatReportQuery query, PageQuery pageQuery);

    /**
     * 获取客户统计数据
     *
     * @param query
     * @param sortQuery
     * @return
     */
    List<CustomerStatReportResp> listCustomerStatReport(CustomerStatReportQuery query, SortQuery sortQuery);

    /**
     * 查询客户报告数据
     *
     * @param query
     * @return
     */
    CustomerStatSummaryResp getCustomerStatReportSummary(CustomerStatReportQuery query);

    /**
     * 获取客户每日统计数据
     *
     * @param query
     * @param pageQuery
     * @return
     */
    PageResp<CustomerDailyStatReportResp> pageCustomerDailyStatReport(CustomerStatReportQuery query,
                                                                      PageQuery pageQuery);

    /**
     * 获取客户每日统计数据
     *
     * @param query
     * @param sortQuery
     * @return
     */
    List<CustomerDailyStatReportResp> listCustomerDailyStatReport(CustomerStatReportQuery query, SortQuery sortQuery);

    /**
     * 通过群ID查找客户
     *
     * @param telegramChatId
     * @return
     */
    CustomerDO getByTelegramChatId(String telegramChatId);

    /**
     * 检测打款金额
     *
     * @param customerId
     * @param amount
     * @return
     */
    boolean checkRepeatTransfer(Long customerId, BigDecimal amount);

    List<CustomerDailyExcel> dailyList(CustomerDO customer);

    CustomerInfoResp getInfo(Long id);

    CustomerDO getByName(String customerName);

    String getName(Long id);

    /**
     * 机器人打款
     *
     * @param customerId
     * @param amount
     * @param feeHandleMethodEnum
     * @param createUser
     * @param remark
     * @return
     */
    Long rechargeFromRobot(Long customerId,
                           BigDecimal amount,
                           RechargeFeeHandleMethodEnum feeHandleMethodEnum,
                           Long createUser,
                           String remark);

    PageResp<CustomerOrderStatisticsResp> selectCustomerOrderStatisticsPage(CustomerOrderStatisticsQuery query,
                                                                            PageQuery pageQuery);

    /**
     * 批量修改客户状态
     *
     * @param ids    ID列表
     * @param status 状态
     */
    void batchUpdateStatus(List<Long> ids, Integer status, LocalDateTime terminateTime);

    /**
     * 开通账户
     *
     * @param id 客户ID
     */
    CustomerOpenResp open(Long id, CustomerOpenReq req);

    /**
     * 重置密码
     *
     * @param id
     * @param req
     */
    void resetPassword(Long id, CustomerOpenReq req);

    /**
     * 激活
     *
     * @param id
     */
    void activation(CustomerActivationReq id);

    /**
     * 批量修改客户关联商务
     *
     * @param ids            客户ID列表
     * @param businessUserId 关联商务用户ID
     */
    void batchUpdateBusinessUser(List<Long> ids, Long businessUserId);

    /**
     * 根据广告户ID获取客户名称
     *
     * @param adAccountId 广告户id
     * @return 客户名称
     */
    String selectCustomerNameByAdAccountId(String adAccountId);

    PageResp<ToufangCustomerResp> selectToufangCustomerPage(CustomerQuery query, PageQuery pageQuery);

    List<ToufangCustomerResp> selectToufangCustomerList(CustomerQuery query, SortQuery sortQuery);

    /**
     * 导出投放客户日报
     *
     * @param customerId
     */
    void exportToufangCustomerDailyReport(Long customerId, HttpServletResponse response);

    void toufangRefund(ToufangRefundReq req);
}