package top.continew.admin.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.biz.enums.*;
import top.continew.admin.biz.mapper.BusinessManagerStatisticsMapper;
import top.continew.admin.biz.model.entity.*;
import top.continew.admin.biz.model.query.BusinessManagerBanStatQuery;
import top.continew.admin.biz.model.query.BusinessManagerChannelStatQuery;
import top.continew.admin.biz.model.query.BusinessManagerStatisticsQuery;
import top.continew.admin.biz.model.req.BusinessManagerStatisticsReq;
import top.continew.admin.biz.model.req.SpendStatisticsReq;
import top.continew.admin.biz.model.resp.*;
import top.continew.admin.biz.service.*;
import top.continew.admin.biz.utils.FacebookUtils;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;
import top.continew.starter.file.excel.util.ExcelUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 成本分析业务实现
 *
 * <AUTHOR>
 * @since 2025/03/17 15:02
 */
@Service
@RequiredArgsConstructor
public class BusinessManagerStatisticsServiceImpl extends BaseServiceImpl<BusinessManagerStatisticsMapper, BusinessManagerStatisticsDO, BusinessManagerStatisticsResp, BusinessManagerStatisticsDetailResp, BusinessManagerStatisticsQuery, BusinessManagerStatisticsReq> implements BusinessManagerStatisticsService {
    private final AdAccountService adAccountService;
    private final BusinessManagerItemService businessManagerItemService;
    private final AdAccountOrderService adAccountOrderService;
    private final CustomerService customerService;
    private final FbAccountService fbAccountService;
    private final PersonalAccountService personalAccountService;
    private final BusinessManagerService businessManagerService;

    /**
     * 统计当天的数据（statDate） 1、剩余正常库存=待出售/特供/已回收、绑定BM5成功的正常广告户 2、当日回收数量=回收状态的下户订单数(回收时间) 2、当天剩余坑位=当天新建数+前一天的未使用正常数
     * 3、当天接入坑位=当天已使用数（使用时间）+当天未使用封禁数（封禁时间） 4、总成本=当天接入坑位数*单价 5、已使用正常（使用时间） 6、未使用正常（总的，不需要时间筛选） 7、已使用封禁（使用时间）
     * 8、未使用封禁（封禁时间） 9、剩余正常坑位（已使用正常+未使用正常） 10、平均备户成本=总成本 / 已使用正常 11、当日封禁（取广告户列表绑定bm5成功、状态封禁、封禁时间为当天的号）
     * 12、当日出售（下户订单状态为授权完成或者接收失败，且完成时间为当天） 13、下户成本 （当日封禁号成本+当日授权完成号成本）/ 当日授权完成 14、存活率=已使用正常 / 当天接入坑位
     * 14、备户存活率=剩余正常坑位/当天剩余坑位
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stat(LocalDate date) {
        // 删除已存在的统计
        baseMapper.delete(Wrappers.lambdaQuery(BusinessManagerStatisticsDO.class)
            .eq(BusinessManagerStatisticsDO::getStatisticsDate, date));

        // 获取统计日期的起止时间
        LocalDateTime startTime = date.atStartOfDay();
        LocalDateTime endTime = date.plusDays(1).atStartOfDay();

        // 获取前一天的日期
        LocalDate previousDate = date.minusDays(1);

        // 1. 剩余正常库存=待出售/特供、绑定BM5成功的正常广告户
        List<AdAccountResp> inventoryList = adAccountService.selectInventoryList();
        long remainingNormalInventory = inventoryList.size();
        JSONObject inventoryJson = new JSONObject();
        // 当日回收数量=回收状态的下户订单数(回收时间)
        List<Long> testCustomerList = customerService.listObjs(Wrappers.<CustomerDO>lambdaQuery()
            .select(CustomerDO::getId)
            .eq(CustomerDO::getIsSelfAccount, true));
        long dailyRecycleCount = adAccountOrderService.count(new LambdaQueryWrapper<AdAccountOrderDO>().notIn(AdAccountOrderDO::getCustomerId, testCustomerList)
            .eq(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.RECYCLE)
            .between(AdAccountOrderDO::getRecycleTime, startTime, endTime));

        // 2、当天剩余坑位=当天新建数+前一天未使用正常数
        long todayNewCount = businessManagerItemService.count(new LambdaQueryWrapper<BusinessManagerItemDO>().between(BusinessManagerItemDO::getCreateTime, startTime, endTime));
        BusinessManagerStatisticsDO previousStatistics = baseMapper.selectOne(new LambdaQueryWrapper<BusinessManagerStatisticsDO>().eq(BusinessManagerStatisticsDO::getStatisticsDate, previousDate)
            .select(BusinessManagerStatisticsDO::getUnusedNormal));
        long dailyRemainingCount = todayNewCount + (null != previousStatistics
            ? previousStatistics.getUnusedNormal()
            : 0);

        // 3 当天接入坑位 = 当天已使用数 + 当天未使用封禁数
        List<BusinessManagerItemDO> usedItems = businessManagerItemService.list(new LambdaQueryWrapper<BusinessManagerItemDO>().between(BusinessManagerItemDO::getUseTime, startTime, endTime));
        List<BusinessManagerItemDO> unusedBanneItems = businessManagerItemService.list(new LambdaQueryWrapper<BusinessManagerItemDO>().between(BusinessManagerItemDO::getBanTime, startTime, endTime)
            .eq(BusinessManagerItemDO::getIsUse, false));
        long dailyReceivedCount = usedItems.size() + unusedBanneItems.size();

        // 4 总成本=当天接入坑位数*单价
        BigDecimal totalCost = usedItems.stream()
            .map(BusinessManagerItemDO::getUnitPrice)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        totalCost = totalCost.add(unusedBanneItems.stream()
            .map(BusinessManagerItemDO::getUnitPrice)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add));

        // 5. 已使用正常（使用时间）
        long usedNormal = usedItems.stream()
            .filter(row -> BusinessManagerStatusEnum.NORMAL.equals(row.getStatus()))
            .count();

        // 6. 未使用正常（总的，不需要时间筛选）
        long unusedNormal = businessManagerItemService.count(new LambdaQueryWrapper<BusinessManagerItemDO>().eq(BusinessManagerItemDO::getIsUse, false)
            .eq(BusinessManagerItemDO::getStatus, BusinessManagerStatusEnum.NORMAL));

        // 7. 已使用封禁（使用时间）
        long usedBanned = businessManagerItemService.count(new LambdaQueryWrapper<BusinessManagerItemDO>().eq(BusinessManagerItemDO::getIsUse, true)
            .eq(BusinessManagerItemDO::getStatus, BusinessManagerStatusEnum.BANNED)
            .between(BusinessManagerItemDO::getUseTime, startTime, endTime));

        // 8. 未使用封禁（封禁时间）
        long unusedBanned = businessManagerItemService.count(new LambdaQueryWrapper<BusinessManagerItemDO>().eq(BusinessManagerItemDO::getIsUse, false)
            .eq(BusinessManagerItemDO::getStatus, BusinessManagerStatusEnum.BANNED)
            .between(BusinessManagerItemDO::getBanTime, startTime, endTime));

        // 9. 剩余正常坑位（已使用正常+未使用正常）
        long remainingNormalCount = usedNormal + unusedNormal;

        // 10. 平均备户成本=总成本 / 已使用正常
        BigDecimal averagePrepareCost = usedNormal > 0
            ? totalCost.divide(BigDecimal.valueOf(usedNormal), 2, RoundingMode.HALF_UP)
            : BigDecimal.ZERO;

        // 11. 当日封禁（取广告户列表绑定bm5成功、状态封禁、封禁时间为当天的号）
        List<AdAccountDO> dailyBannedAdAccounts = adAccountService.list(new LambdaQueryWrapper<AdAccountDO>().eq(AdAccountDO::getAccountStatus, AdAccountStatusEnum.BANNED)
            .eq(AdAccountDO::getKeepStatus, AdAccountKeepStatusEnum.SUCCESS)
            .between(AdAccountDO::getBanTime, startTime, endTime));
        //当日封禁数
        long dailyBanned = dailyBannedAdAccounts.size();
        ;
        //当日封禁成本
        BigDecimal dailyBannedCost = dailyBannedAdAccounts.stream()
            .map(AdAccountDO::getCost)
            .filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 12. 当日出售（出售时间或者bm授权时间为当天）
        // 需要忽略的客户下户数据
        List<AdAccountOrderDO> ignoreOrderList = adAccountOrderService.list(new LambdaQueryWrapper<AdAccountOrderDO>().in(AdAccountOrderDO::getCustomerId, testCustomerList)
            .eq(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED)
            .between(AdAccountOrderDO::getFinishTime, startTime, endTime));
        List<String> ignoreSaleAdAccountList = ignoreOrderList.stream().map(AdAccountOrderDO::getAdAccountId).toList();
        // 当天的出售账户
        List<AdAccountDO> saleAdAccountList = adAccountService.list(Wrappers.<AdAccountDO>lambdaQuery()
            .eq(AdAccountDO::getSaleStatus, AdAccountSaleStatusEnum.SALT)
            .eq(AdAccountDO::getKeepStatus, AdAccountKeepStatusEnum.SUCCESS)
            .and(v -> v.between(AdAccountDO::getSaleTime, startTime, endTime)
                .or()
                .between(AdAccountDO::getBmAuthTime, startTime, endTime)));
        // 去除忽略的客户数据
        List<AdAccountDO> realSaledAdAccountList = saleAdAccountList.stream()
            .filter(v -> !ignoreSaleAdAccountList.contains(v.getPlatformAdId()))
            .toList();
        long dailySalesForAuthComplete = realSaledAdAccountList.size();
        long dailySalesForBm = realSaledAdAccountList.stream().filter(v -> v.getBusinessManagerId() != null).count();
        //计算当日出售BM正常号成本
        BigDecimal dailySalesForAuthCompleteCost = realSaledAdAccountList.stream()
            .filter(v -> v.getAccountStatus().equals(AdAccountStatusEnum.NORMAL) && v.getBusinessManagerId() != null)
            .map(AdAccountDO::getCost) // 取出 cost
            .filter(Objects::nonNull) // 过滤掉 cost 为 null 的情况
            .reduce(BigDecimal.ZERO, BigDecimal::add); // 累加

        // 13. 下户成本=（当日封禁号成本+当日授权完成号成本）/ 当日授权完成BM户
        BigDecimal orderCost = dailySalesForAuthComplete > 0
            ? (dailyBannedCost.add(dailySalesForAuthCompleteCost)).divide(BigDecimal.valueOf(dailySalesForBm), 2, RoundingMode.HALF_UP)
            : BigDecimal.ZERO;

        // 存活率=已使用正常 / 当天接入坑位
        BigDecimal survivalRate = dailyReceivedCount > 0 ? BigDecimal.valueOf(usedNormal)
            .divide(BigDecimal.valueOf(dailyReceivedCount), 4, RoundingMode.HALF_UP)
            .multiply(BigDecimal.valueOf(100)) : BigDecimal.ZERO;

        // 每日采购成本=大黑号+BM1+BM坑位+三解号
        BigDecimal daheiCost = fbAccountService.list(Wrappers.<FbAccountDO>lambdaQuery()
                .between(FbAccountDO::getCreateTime, startTime, endTime))
            .stream()
            .map(FbAccountDO::getPrice)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal bm1Cost = businessManagerService.list(Wrappers.<BusinessManagerDO>lambdaQuery()
                .eq(BusinessManagerDO::getType, BusinessManagerTypeEnum.BM1.getLongValue())
                .eq(BusinessManagerDO::getIsBu, false)
                .between(BusinessManagerDO::getCreateTime, startTime, endTime))
            .stream()
            .map(BusinessManagerDO::getUnitPrice)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal sanjieCost = personalAccountService.list(Wrappers.<PersonalAccountDO>lambdaQuery()
                .eq(PersonalAccountDO::getIsAfterSale, false)
                .between(PersonalAccountDO::getCreateTime, startTime, endTime))
            .stream()
            .map(PersonalAccountDO::getUnitPrice)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal bmItemCost = businessManagerItemService.list(Wrappers.<BusinessManagerItemDO>lambdaQuery()
                .eq(BusinessManagerItemDO::getIsBu, false)
                .ne(BusinessManagerItemDO::getType, BusinessManagerTypeEnum.BM1.getLongValue())
                .between(BusinessManagerItemDO::getCreateTime, startTime, endTime))
            .stream()
            .map(BusinessManagerItemDO::getUnitPrice)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 保存统计结果
        BusinessManagerStatisticsDO statistics = new BusinessManagerStatisticsDO();
        statistics.setStatisticsDate(date);
        statistics.setPurchaseCost(daheiCost.add(bm1Cost).add(sanjieCost).add(bmItemCost));
        statistics.setRemainingNormalInventory(remainingNormalInventory);
        statistics.setInventoryDetail(JSON.toJSONString(inventoryJson));
        statistics.setDailyReceivedCount(dailyReceivedCount);
        statistics.setTotalCost(totalCost);
        statistics.setDailyRecycleCount(dailyRecycleCount);
        statistics.setUsedNormal(usedNormal);
        statistics.setUnusedNormal(unusedNormal);
        statistics.setUsedBanned(usedBanned);
        statistics.setUnusedBanned(unusedBanned);
        statistics.setRemainingNormalCount(remainingNormalCount);
        statistics.setDailyRemainingCount(dailyRemainingCount);
        statistics.setSurvivalRate(survivalRate);
        statistics.setAveragePrepareCost(averagePrepareCost);
        statistics.setPrepareSurvivalRate(BigDecimal.ZERO);
        statistics.setDailySales(adAccountOrderService.count(new LambdaQueryWrapper<AdAccountOrderDO>().notIn(AdAccountOrderDO::getCustomerId, testCustomerList)
            .eq(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED)
            .between(AdAccountOrderDO::getFinishTime, startTime, endTime)));
        statistics.setDailySalesForBm(dailySalesForBm);
        statistics.setDailyBanned(dailyBanned);
        statistics.setOrderCost(orderCost);
        statistics.setCreateUser(0L);
        save(statistics);

        //更新下户订单的成本
        updateAdAccountOrderCost(realSaledAdAccountList, orderCost);

    }

    /**
     * 更新下户订单的成本
     */
    private void updateAdAccountOrderCost(List<AdAccountDO> saleAdAccountList, BigDecimal orderCost) {
        for (AdAccountDO accountDO : saleAdAccountList) {
            adAccountOrderService.update(Wrappers.<AdAccountOrderDO>lambdaUpdate()
                .set(AdAccountOrderDO::getCost, orderCost)
                .eq(AdAccountOrderDO::getAdAccountId, accountDO.getPlatformAdId())
                .eq(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED));
        }
    }

    @Override
    public SpendStatisticsSummaryResp getSpendSummary(SpendStatisticsReq req) {
        return baseMapper.selectSpendSummary(req);
    }

    @Override
    public List<DailySpendResp> getDailySpend(SpendStatisticsReq req) {
        BigDecimal profitRate = new BigDecimal("0.024");
        List<DailySpendResp> list = baseMapper.selectDailySpend(req);
        for (DailySpendResp dailySpendResp : list) {
            if (dailySpendResp.getTotalSpend().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal profit = NumberUtil.mul(profitRate, dailySpendResp.getTotalSpend())
                    .setScale(2, RoundingMode.HALF_UP);
                dailySpendResp.setProfit(profit);
            }
        }
        return list;
    }

    @Override
    public BusinessManagerStatisticsSummaryResp getSummary(BusinessManagerStatisticsQuery query) {
        return this.baseMapper.getSummary(query);
    }

    @Override
    public PageResp<BusinessManagerChannelStatDataResp> statChannelData(BusinessManagerChannelStatQuery query) {
        IPage<BusinessManagerChannelStatDataResp> page = baseMapper.listStatChannelData(new Page<>(query.getPage(), query.getSize()), query);

        if (CollUtil.isEmpty(page.getRecords())) {
            return new PageResp<>(page.getRecords(), page.getTotal());
        }

        List<Long> channelIds = page.getRecords()
            .stream()
            .map(BusinessManagerChannelStatDataResp::getChannelId)
            .collect(Collectors.toList());

        List<Map<String, Object>> channelAdData = baseMapper.listStatChannelAdData(query.getBmType(), channelIds);
        List<Map<String, Object>> channelAdOrderData = baseMapper.listStatChannelAdOrderData(query.getBmType(), channelIds);

        page.getRecords().forEach(row -> {
            row.setAdAccountTotalCount(0L);
            row.setAdAccountInvalidCount(0L);
            row.setAdAccountOrderNoSpendCount(0L);
            row.setAdAccountOrderCount(0L);
            row.setAdAccountInvalidRatio("0");
            row.setAdAccountOrderNoSpendRatio("0");

            channelAdData.forEach(channelMap -> {
                Long channelId = MapUtil.getLong(channelMap, "channelId");

                if (row.getChannelId().equals(channelId)) {
                    row.setAdAccountTotalCount(null == channelMap.get("adAccountTotalCount")
                        ? 0L
                        : MapUtil.getLong(channelMap, "adAccountTotalCount"));
                    row.setAdAccountInvalidCount(null == channelMap.get("adAccountInvalidCount")
                        ? 0L
                        : MapUtil.getLong(channelMap, "adAccountInvalidCount"));
                    //计算出占比
                    if (row.getAdAccountTotalCount() > 0L) {
                        BigDecimal ratio = BigDecimal.valueOf(row.getAdAccountInvalidCount())
                            .divide(BigDecimal.valueOf(row.getAdAccountTotalCount()), 4, RoundingMode.HALF_UP)
                            .setScale(2, RoundingMode.HALF_UP);

                        row.setAdAccountInvalidRatio(ratio.toString());
                    }
                }
            });

            channelAdOrderData.forEach(channelMap -> {
                Long channelId = MapUtil.getLong(channelMap, "channelId");

                if (row.getChannelId().equals(channelId)) {
                    row.setAdAccountOrderCount(null == channelMap.get("adAccountOrderCount")
                        ? 0L
                        : MapUtil.getLong(channelMap, "adAccountOrderCount"));
                    row.setAdAccountOrderNoSpendCount(null == channelMap.get("adAccountOrderNoSpendCount")
                        ? 0L
                        : MapUtil.getLong(channelMap, "adAccountOrderNoSpendCount"));
                    if (row.getAdAccountOrderCount() > 0L) {
                        BigDecimal ratio = BigDecimal.valueOf(row.getAdAccountOrderNoSpendCount())
                            .divide(BigDecimal.valueOf(row.getAdAccountOrderCount()), 4, RoundingMode.HALF_UP)
                            .setScale(2, RoundingMode.HALF_UP);
                        row.setAdAccountOrderNoSpendRatio(ratio.toString());
                    }
                }
            });
        });

        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    public void export(BusinessManagerStatisticsQuery query, SortQuery sortQuery, HttpServletResponse response) {
        List<BusinessManagerStatisticsDetailResp> list = this.list(query, sortQuery, this.getDetailClass());
        for (BusinessManagerStatisticsDetailResp businessManagerStatisticsDetailResp : list) {
            if (StringUtils.isNotBlank(businessManagerStatisticsDetailResp.getInventoryDetail())) {
                JSONObject jsonObject = JSON.parseObject(businessManagerStatisticsDetailResp.getInventoryDetail());
                businessManagerStatisticsDetailResp.setRecycleNum(jsonObject.getInteger("recycle"));
                businessManagerStatisticsDetailResp.setAppealNum(jsonObject.getInteger("appeal"));
                businessManagerStatisticsDetailResp.setBm5Num(jsonObject.getInteger("bm5"));
                businessManagerStatisticsDetailResp.setBm250Num(jsonObject.getInteger("bm250"));
                businessManagerStatisticsDetailResp.setBm2500Num(jsonObject.getInteger("bm2500"));
                businessManagerStatisticsDetailResp.setBm10000Num(jsonObject.getInteger("bm10000"));
                businessManagerStatisticsDetailResp.setShortNum(jsonObject.getInteger("short"));
            }
        }
        ExcelUtils.export(list, "导出数据", this.getDetailClass(), response);
    }

    @Override
    public PageResp<BusinessManagerBanStatDataResp> statBanData(BusinessManagerBanStatQuery query) {
        IPage<BusinessManagerBanStatDataResp> page = baseMapper.listStatBanData(new Page<>(query.getPage(), query.getSize()), query);
        return new PageResp<>(page.getRecords(), page.getTotal());
    }
}