package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.entity.BusinessManagerStatisticsDO;
import top.continew.admin.biz.model.query.BusinessManagerBanStatQuery;
import top.continew.admin.biz.model.query.BusinessManagerChannelStatQuery;
import top.continew.admin.biz.model.query.BusinessManagerStatisticsQuery;
import top.continew.admin.biz.model.req.SpendStatisticsReq;
import top.continew.admin.biz.model.resp.*;
import top.continew.starter.data.mp.base.BaseMapper;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 成本分析 Mapper
 *
 * <AUTHOR>
 * @since 2025/03/17 15:02
 */
public interface BusinessManagerStatisticsMapper extends BaseMapper<BusinessManagerStatisticsDO> {
    /**
     * 查询消耗统计汇总数据
     *
     * @param req 统计请求
     * @return 汇总数据
     */
    SpendStatisticsSummaryResp selectSpendSummary(@Param("req") SpendStatisticsReq req);

    /**
     * 查询每日消耗记录
     *
     * @param req 统计请求
     * @return 每日消耗记录列表
     */
    List<DailySpendResp> selectDailySpend(@Param("req") SpendStatisticsReq req);

    /**
     * 获取成本分析汇总数据
     *
     * @param query
     * @return
     */
    BusinessManagerStatisticsSummaryResp getSummary(@Param("query") BusinessManagerStatisticsQuery query);

    /**
     * 统计渠道数据
     * @param query
     * @return
     */
    IPage<BusinessManagerChannelStatDataResp> listStatChannelData(@Param("page") IPage<BusinessManagerChannelStatDataResp> page,
                                                                  @Param("query") BusinessManagerChannelStatQuery query);

    IPage<BusinessManagerBanStatDataResp> listStatBanData(@Param("page") IPage<BusinessManagerBanStatDataResp> page,  @Param("query") BusinessManagerBanStatQuery query);

    List<Map<String, Object>> listStatChannelAdData(@Param("bmType") Long bmType, @Param("channelIds") List<Long> channelIds);

    List<Map<String, Object>> listStatChannelAdOrderData(@Param("bmType") Long bmType, @Param("channelIds") List<Long> channelIds);
}