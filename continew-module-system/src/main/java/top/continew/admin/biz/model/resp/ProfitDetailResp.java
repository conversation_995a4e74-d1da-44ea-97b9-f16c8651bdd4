package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import top.continew.admin.biz.enums.AdProductTypeEnum;
import top.continew.admin.biz.enums.AdProjectEnum;
import top.continew.admin.biz.enums.TransactionUserTypeEnum;
import top.continew.admin.common.base.BaseDetailResp;

/**
 * 利润详情信息
 *
 * <AUTHOR>
 * @since 2025/08/16 16:14
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "利润详情信息")
public class ProfitDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 媒体平台
     */
    @Schema(description = "媒体平台")
    @ExcelProperty(value = "媒体平台")
    private AdProductTypeEnum adPlatform;

    /**
     * 项目
     */
    @Schema(description = "项目")
    @ExcelProperty(value = "项目")
    private AdProjectEnum project;

    @ExcelProperty(value = "交易用户类型")
    private TransactionUserTypeEnum transactionUserType;

    @ExcelProperty(value = "交易用户")
    private String transactionUserName;

    /**
     * 类型
     */
    @Schema(description = "类型")
    private Long type;

    @ExcelProperty(value = "物料类型")
    private String typeName;

    /**
     * 交易金额
     */
    @Schema(description = "交易金额")
    @ExcelProperty(value = "交易金额")
    private BigDecimal amount;

    @ExcelProperty(value = "交易数量")
    private Integer num;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间")
    @ExcelProperty(value = "交易时间")
    private LocalDateTime transTime;

    /**
     * 交易哈希
     */
    @Schema(description = "交易哈希")
    @ExcelProperty(value = "交易哈希")
    private String transactionHash;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @ExcelProperty(value = "备注")
    private String remark;

    private Long transactionUserId;
}