package top.continew.admin.biz.model.resp;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.AdPlatformEnum;
import top.continew.admin.biz.enums.AdProjectEnum;
import top.continew.admin.biz.enums.PurchaseOrderStatusEnum;
import top.continew.admin.biz.enums.PurchaseOrderTypeEnum;
import top.continew.admin.biz.excel.converter.BooleanConverter;
import top.continew.admin.common.base.BaseDetailResp;
import top.continew.starter.file.excel.converter.ExcelBaseEnumConverter;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 采购订单详情信息
 *
 * <AUTHOR>
 * @since 2025/05/21 14:38
 */
@Data
@ExcelIgnoreUnannotated
@Schema(description = "采购订单详情信息")
public class PurchaseOrderDetailResp extends BaseDetailResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 渠道ID
     */
    @Schema(description = "渠道ID")
    private Long channelId;

    /**
     * 媒体平台
     */
    @ExcelProperty(value = "广告平台", converter = ExcelBaseEnumConverter.class)
    private AdPlatformEnum adPlatform;

    /**
     * 项目
     */
    @ExcelProperty(value = "所属项目", converter = ExcelBaseEnumConverter.class)
    private AdProjectEnum project;

    @ExcelProperty("渠道")
    private String channelName;

    /**
     * 物料类型
     */
    @Schema(description = "物料类型")
    private Long type;

    @ExcelProperty(value = "物料类型")
    private String typeName;

    /**
     * 预计采购数量
     */
    @Schema(description = "预计采购数量")
    @ExcelProperty("采购数量")
    private Integer expectNum;

    /**
     * 预计采购金额
     */
    @Schema(description = "采购金额")
    @ExcelProperty("采购金额")
    private BigDecimal totalPrice;

    @ExcelProperty("验收数量")
    private Integer receiveNum;

    @ExcelProperty("验收金额")
    private BigDecimal receivePrice;

    @ExcelProperty("验收人")
    private String receiveUser;
    @ExcelProperty("验收日期")
    private LocalDate receiveDate;

    @ExcelProperty("入库数量")
    private Integer writeNum;
    @ExcelProperty("入库金额")
    private BigDecimal writePrice;

    /**
     * 是否付款
     */
    @Schema(description = "是否付款")
    @ExcelProperty(value = "是否付款", converter = BooleanConverter.class)
    private Boolean isPay;

    /**
     * 付款时间
     */
    @Schema(description = "付款时间")
    @ExcelProperty("付款时间")
    private LocalDateTime payTime;

    /**
     * 付款金额
     */
    @Schema(description = "付款金额")
    @ExcelProperty("付款金额")
    private BigDecimal payPrice;

    /**
     * 状态（1=进行中，2=已完成）
     */
    @Schema(description = "状态（1=进行中，2=已完成）")
    @ExcelProperty(value = "状态", converter = ExcelBaseEnumConverter.class)
    private PurchaseOrderStatusEnum status;

    /**
     * 完成时间
     */
    @Schema(description = "完成时间")
    @ExcelProperty("完成时间")
    private LocalDateTime finishTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @ExcelProperty("备注")
    private String remark;

    private LocalDate purchaseTime;

    private Boolean openReceive;
}