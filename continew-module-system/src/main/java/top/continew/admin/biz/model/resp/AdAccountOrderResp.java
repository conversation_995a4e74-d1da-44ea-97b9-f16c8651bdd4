/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import top.continew.admin.biz.enums.AdAccountClearStatusEnum;
import top.continew.admin.biz.enums.AdAccountOrderStatusEnum;
import top.continew.admin.biz.enums.AdAccountStatusEnum;
import top.continew.admin.common.base.BaseResp;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 下户订单信息
 *
 * <AUTHOR>
 * @since 2024/12/30 17:59
 */
@Data
@Schema(description = "下户订单信息")
public class AdAccountOrderResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNo;

    /**
     * 关联客户
     */
    @Schema(description = "关联客户")
    private Long customerId;

    /**
     * 关联广告户
     */
    @Schema(description = "关联广告户")
    private String adAccountId;

    private String adAccountName;

    /**
     * 客户BM ID
     */
    @Schema(description = "客户BM ID")
    private String customerBmId;

    /**
     * 开户费
     */
    @Schema(description = "开户费")
    private BigDecimal payAmount;

    /**
     * 支付时间
     */
    @Schema(description = "支付时间")
    private LocalDateTime payTime;

    /**
     * 终止时间
     */
    @Schema(description = "终止时间")
    private LocalDateTime endTime;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private AdAccountOrderStatusEnum status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 处理人
     */
    @Schema(description = "处理人")
    private Long handleUser;

    /**
     * 处理时间
     */
    @Schema(description = "处理时间")
    private LocalDateTime handleTime;

    /**
     * 授权时间
     */
    @Schema(description = "授权时间")
    private LocalDateTime finishTime;

    private LocalDateTime recycleTime;

    private AdAccountStatusEnum accountStatus;

    private LocalDateTime banTime;

    private AdAccountClearStatusEnum clearStatus;

    private LocalDateTime clearTime;

    private BigDecimal spendCap;

    private BigDecimal amountSpent;

    private BigDecimal totalSpent;

    private BigDecimal rechargeAmount;

    private String browserNo;

    private String customerName;

    private String handleUserName;

    private BigDecimal cardBalance;

    private String adAccountBmId;

    private String timezone;

    private BigDecimal cardSpent;

    private BigDecimal accountCost;

    /**
     * 下户成本
     */
    private BigDecimal cost;

    private Long bmType;

    private Boolean refunded;
    /**
     * 上系列时间
     */
    private LocalDateTime startCampaignTime;

    private String campaignDesc;

    private String tags;

    private String channelName;

    private Boolean enablePrepay;

    private BigDecimal prepayAccountBalance;

    private Boolean isOneDollar;

    private LocalDateTime bmAuthTime;

    private BigDecimal fbBalance;

    private String customerEmail;

    private Boolean takeStatus;

    private Integer costParty;

    private Integer orderMethod;
}