package top.continew.admin.biz.model.resp;

import java.io.Serial;
import java.time.*;
import java.math.BigDecimal;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import top.continew.admin.biz.enums.AdProductTypeEnum;
import top.continew.admin.biz.enums.AdProjectEnum;
import top.continew.admin.common.base.BaseResp;

/**
 * 利润信息
 *
 * <AUTHOR>
 * @since 2025/08/16 16:14
 */
@Data
@Schema(description = "利润信息")
public class ProfitResp extends BaseResp {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 媒体平台
     */
    @Schema(description = "媒体平台")
    private AdProductTypeEnum adPlatform;

    /**
     * 项目
     */
    @Schema(description = "项目")
    private AdProjectEnum project;

    /**
     * 类型
     */
    @Schema(description = "类型")
    private Long type;

    private String typeName;



    /**
     * 交易金额
     */
    @Schema(description = "交易金额")
    private BigDecimal amount;

    /**
     * 交易时间
     */
    @Schema(description = "交易时间")
    private LocalDateTime transTime;

    /**
     * 交易哈希
     */
    @Schema(description = "交易哈希")
    private String transactionHash;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    private Long transactionUserId;

    private Integer transactionUserType;

    private Integer num;

    private String transactionUserName;
}